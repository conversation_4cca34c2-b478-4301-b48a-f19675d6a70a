#include "GuildManager.h"
#include "GameEngine.h"
#include "../Common/Utils.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <algorithm>
#include <sstream>
#include <fstream>
#include <filesystem>

namespace MirServer {

// ============================================================================
// Guild 类实现
// ============================================================================

Guild::Guild(const std::string& name) : m_guildName(name) {
    m_lastSaveTime = GetCurrentTime();
    Logger::Info("Guild created: " + name);
}

Guild::~Guild() {
    // 保存数据
    if (m_changed) {
        SaveToFile();
    }
    Logger::Info("Guild destroyed: " + m_guildName);
}

const std::string& Guild::GetChiefName() const {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 查找会长
    for (const auto& member : m_members) {
        if (member.rank == GuildRank::CHIEF) {
            return member.playerName;
        }
    }

    static std::string empty;
    return empty;
}

int Guild::GetMemberCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return static_cast<int>(m_members.size());
}

bool Guild::IsFull() const {
    return GetMemberCount() >= MAX_GUILD_MEMBERS;
}

void Guild::SetBuildPoint(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_buildPoint = point;
    m_changed = true;
}

void Guild::SetAurae(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_aurae = point;
    m_changed = true;
}

void Guild::SetStability(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_stability = point;
    m_changed = true;
}

void Guild::SetFlourishing(int point) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_flourishing = point;
    m_changed = true;
}

void Guild::SetChiefItemCount(int count) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_chiefItemCount = count;
    m_changed = true;
}

bool Guild::AddMember(PlayObject* player, GuildRank rank) {
    if (!player) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已满员
    if (IsFull()) {
        return false;
    }

    // 检查是否已经是成员
    if (IsMember(player->GetCharName())) {
        return false;
    }

    // 添加成员
    GuildMember member(player->GetCharName(), rank);
    member.playerObject = player;
    member.isOnline = true;
    member.lastOnlineTime = GetCurrentTime();

    // 设置职位名称
    switch (rank) {
        case GuildRank::CHIEF:
            member.rankName = "会长";
            break;
        case GuildRank::VICE_CHIEF:
            member.rankName = "副会长";
            break;
        case GuildRank::CAPTAIN:
            member.rankName = "队长";
            break;
        case GuildRank::MEMBER:
        default:
            member.rankName = "成员";
            break;
    }

    m_members.push_back(member);

    // 更新玩家的行会信息
    auto& humData = const_cast<HumDataInfo&>(player->GetHumDataInfo());
    humData.guildName = m_guildName;
    humData.guildRank = static_cast<BYTE>(rank);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送行会消息
    SendGuildMessage(player->GetCharName() + " 加入了行会");

    Logger::Info("Player " + player->GetCharName() + " joined guild " + m_guildName);
    return true;
}

bool Guild::RemoveMember(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    if (it == m_members.end()) {
        return false;
    }

    // 如果玩家在线，清除其行会信息
    if (it->playerObject && it->isOnline) {
        auto& humData = const_cast<HumDataInfo&>(it->playerObject->GetHumDataInfo());
        humData.guildName.clear();
        humData.guildRank = 0;

        // 通知玩家行会变化
        it->playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
    }

    // 发送行会消息
    SendGuildMessage(playerName + " 离开了行会");

    // 移除成员
    m_members.erase(it);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    Logger::Info("Player " + playerName + " left guild " + m_guildName);
    return true;
}

bool Guild::IsMember(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });
}

GuildMember* Guild::FindMember(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? &(*it) : nullptr;
}

bool Guild::UpdateMemberRank(const std::string& playerName, GuildRank newRank, const std::string& rankName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    if (it == m_members.end()) {
        return false;
    }

    // 更新职位
    it->rank = newRank;
    it->rankName = rankName;

    // 如果玩家在线，更新其行会信息
    if (it->playerObject && it->isOnline) {
        auto& humData = const_cast<HumDataInfo&>(it->playerObject->GetHumDataInfo());
        humData.guildRank = static_cast<BYTE>(newRank);

        // 通知玩家职位变化
        it->playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
    }

    // 标记需要保存
    m_changed = true;

    Logger::Info("Updated rank for " + playerName + " in guild " + m_guildName + " to " + rankName);
    return true;
}

std::string Guild::GetRankName(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? it->rankName : "";
}

GuildRank Guild::GetMemberRank(const std::string& playerName) const {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [&playerName](const GuildMember& member) {
            return member.playerName == playerName;
        });

    return (it != m_members.end()) ? it->rank : GuildRank::MEMBER;
}

bool Guild::StartWar(Guild* targetGuild, DWORD duration) {
    if (!targetGuild || targetGuild == this) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经在战争中
    if (IsWarWith(targetGuild)) {
        return false;
    }

    // 检查是否是联盟
    if (IsAlly(targetGuild)) {
        return false;
    }

    // 创建战争信息
    GuildWarInfo warInfo(m_guildName, targetGuild->GetGuildName(), duration);
    m_guildWars.push_back(warInfo);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送战争开始消息
    std::string message = "与 " + targetGuild->GetGuildName() + " 的行会战争开始！";
    SendGuildMessage(message);

    Logger::Info("Guild war started between " + m_guildName + " and " + targetGuild->GetGuildName());
    return true;
}

bool Guild::EndWar(Guild* targetGuild) {
    if (!targetGuild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildWars.begin(), m_guildWars.end(),
        [targetGuild](const GuildWarInfo& war) {
            return war.isActive &&
                   (war.guild1 == targetGuild->GetGuildName() ||
                    war.guild2 == targetGuild->GetGuildName());
        });

    if (it == m_guildWars.end()) {
        return false;
    }

    // 结束战争
    it->isActive = false;

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送战争结束消息
    std::string message = "与 " + targetGuild->GetGuildName() + " 的行会战争结束！";
    SendGuildMessage(message);

    Logger::Info("Guild war ended between " + m_guildName + " and " + targetGuild->GetGuildName());
    return true;
}

bool Guild::IsWarWith(Guild* guild) const {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_guildWars.begin(), m_guildWars.end(),
        [guild](const GuildWarInfo& war) {
            return war.isActive &&
                   (war.guild1 == guild->GetGuildName() ||
                    war.guild2 == guild->GetGuildName());
        });
}

bool Guild::IsNotWarGuild(Guild* guild) const {
    return !IsWarWith(guild);
}

bool Guild::AddAlly(Guild* guild) {
    if (!guild || guild == this) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否已经是联盟
    if (IsAlly(guild)) {
        return false;
    }

    // 检查是否在战争中
    if (IsWarWith(guild)) {
        return false;
    }

    // 添加联盟
    GuildAllyInfo allyInfo(guild->GetGuildName());
    m_guildAllies.push_back(allyInfo);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送联盟消息
    std::string message = "与 " + guild->GetGuildName() + " 结成联盟！";
    SendGuildMessage(message);

    Logger::Info("Guild alliance formed between " + m_guildName + " and " + guild->GetGuildName());
    return true;
}

bool Guild::RemoveAlly(Guild* guild) {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guildAllies.begin(), m_guildAllies.end(),
        [guild](const GuildAllyInfo& ally) {
            return ally.guildName == guild->GetGuildName();
        });

    if (it == m_guildAllies.end()) {
        return false;
    }

    // 移除联盟
    m_guildAllies.erase(it);

    // 标记需要保存
    m_changed = true;
    UpdateGuildFile();

    // 发送解除联盟消息
    std::string message = "与 " + guild->GetGuildName() + " 解除联盟！";
    SendGuildMessage(message);

    Logger::Info("Guild alliance removed between " + m_guildName + " and " + guild->GetGuildName());
    return true;
}

bool Guild::IsAlly(Guild* guild) const {
    if (!guild) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    return std::any_of(m_guildAllies.begin(), m_guildAllies.end(),
        [guild](const GuildAllyInfo& ally) {
            return ally.isActive && ally.guildName == guild->GetGuildName();
        });
}

void Guild::AddNotice(const std::string& notice) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 添加公告到列表开头
    m_notices.insert(m_notices.begin(), notice);

    // 限制公告数量
    if (m_notices.size() > MAX_NOTICES) {
        m_notices.resize(MAX_NOTICES);
    }

    // 标记需要保存
    m_changed = true;

    Logger::Info("Notice added to guild " + m_guildName + ": " + notice);
}

void Guild::ClearNotices() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_notices.clear();
    m_changed = true;

    Logger::Info("Notices cleared for guild " + m_guildName);
}

void Guild::SendGuildMessage(const std::string& message) {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 向所有在线成员发送消息
    for (auto& member : m_members) {
        if (member.isOnline && member.playerObject) {
            member.playerObject->SendMessage("[行会] " + message, 0);
        }
    }

    Logger::Debug("Guild message sent to " + m_guildName + ": " + message);
}

void Guild::SendGuildNotice(const std::string& notice) {
    AddNotice(notice);
    SendGuildMessage("公告: " + notice);
}

void Guild::UpdateGuildFile() {
    m_changed = true;
    m_lastSaveTime = GetCurrentTime();
    SaveToFile();
}

void Guild::CheckSaveGuildFile() {
    if (m_changed && (GetCurrentTime() - m_lastSaveTime) > SAVE_INTERVAL) {
        m_changed = false;
        SaveToFile();
    }
}

void Guild::OnPlayerLogin(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [player](GuildMember& member) {
            return member.playerName == player->GetCharName();
        });

    if (it != m_members.end()) {
        it->playerObject = player;
        it->isOnline = true;
        it->lastOnlineTime = GetCurrentTime();

        // 更新玩家的行会信息
        auto& humData = const_cast<HumDataInfo&>(player->GetHumDataInfo());
        humData.guildName = m_guildName;
        humData.guildRank = static_cast<BYTE>(it->rank);

        // 通知玩家行会信息
        player->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);

        Logger::Debug("Guild member " + player->GetCharName() + " logged in to guild " + m_guildName);
    }
}

void Guild::OnPlayerLogout(PlayObject* player) {
    if (!player) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_members.begin(), m_members.end(),
        [player](GuildMember& member) {
            return member.playerName == player->GetCharName();
        });

    if (it != m_members.end()) {
        it->playerObject = nullptr;
        it->isOnline = false;
        it->lastOnlineTime = GetCurrentTime();

        Logger::Debug("Guild member " + player->GetCharName() + " logged out from guild " + m_guildName);
    }
}

void Guild::StartTeamFight() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_teamFightActive = true;
    m_contestPoint = 0;
    m_teamFightDeadList.clear();

    SendGuildMessage("团队战开始！");
    Logger::Info("Team fight started for guild " + m_guildName);
}

void Guild::EndTeamFight() {
    std::lock_guard<std::mutex> lock(m_mutex);

    m_teamFightActive = false;

    SendGuildMessage("团队战结束！最终积分: " + std::to_string(m_contestPoint));
    Logger::Info("Team fight ended for guild " + m_guildName + " with score " + std::to_string(m_contestPoint));
}

void Guild::AddTeamFightMember(const std::string& playerName) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find(m_teamFightDeadList.begin(), m_teamFightDeadList.end(), playerName);
    if (it == m_teamFightDeadList.end()) {
        m_teamFightDeadList.push_back(playerName);
    }
}

void Guild::TeamFightWhoDead(const std::string& playerName) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // TODO: 实现团队战死亡统计
    Logger::Debug("Team fight death recorded for " + playerName + " in guild " + m_guildName);
}

void Guild::TeamFightWhoWinPoint(const std::string& playerName, int point) {
    if (!m_teamFightActive) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    m_contestPoint += point;

    Logger::Debug("Team fight points added: " + std::to_string(point) + " for " + playerName + " in guild " + m_guildName);
}

void Guild::Run() {
    std::lock_guard<std::mutex> lock(m_mutex);

    DWORD currentTime = GetCurrentTime();
    bool warChanged = false;

    // 检查战争是否过期
    for (auto& war : m_guildWars) {
        if (war.isActive && (currentTime - war.startTime) > war.duration) {
            war.isActive = false;
            warChanged = true;

            // 发送战争结束消息
            std::string targetGuild = (war.guild1 == m_guildName) ? war.guild2 : war.guild1;
            SendGuildMessage("与 " + targetGuild + " 的行会战争时间到期！");
        }
    }

    if (warChanged) {
        m_changed = true;
        UpdateGuildFile();
    }

    // 检查是否需要保存
    CheckSaveGuildFile();
}

bool Guild::SetGuildInfo(const std::string& chiefName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_members.empty()) {
        // 创建会长成员
        GuildMember chief(chiefName, GuildRank::CHIEF);
        chief.rankName = "会长";
        m_members.push_back(chief);

        m_changed = true;
        SaveToFile();

        Logger::Info("Guild chief set for " + m_guildName + ": " + chiefName);
        return true;
    }

    return false;
}

// ============================================================================
// Guild 文件操作实现
// ============================================================================

bool Guild::LoadFromFile() {
    std::string fileName = m_guildName + ".txt";
    std::string configFileName = m_guildName + ".ini";

    bool result = LoadGuildFile(fileName);
    LoadGuildConfig(configFileName);

    return result;
}

bool Guild::SaveToFile() {
    std::string fileName = m_guildName + ".txt";
    std::string configFileName = m_guildName + ".ini";

    SaveGuildFile(fileName);
    SaveGuildConfig(configFileName);

    return true;
}

void Guild::BackupGuildFile() {
    std::string backupFileName = m_guildName + "." + std::to_string(GetCurrentTime()) + ".bak";
    SaveGuildFile(backupFileName);

    Logger::Info("Guild file backed up: " + backupFileName);
}

bool Guild::LoadGuildFile(const std::string& fileName) {
    // TODO: 实现从文件加载行会数据
    // 这里需要根据原项目的文件格式来实现
    Logger::Info("Loading guild file: " + fileName);
    return true;
}

bool Guild::LoadGuildConfig(const std::string& fileName) {
    // TODO: 实现从配置文件加载行会配置
    Logger::Info("Loading guild config: " + fileName);
    return true;
}

void Guild::SaveGuildFile(const std::string& fileName) {
    // TODO: 实现保存行会数据到文件
    // 这里需要根据原项目的文件格式来实现
    Logger::Info("Saving guild file: " + fileName);
}

void Guild::SaveGuildConfig(const std::string& fileName) {
    // TODO: 实现保存行会配置到文件
    Logger::Info("Saving guild config: " + fileName);
}

void Guild::ClearRanks() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_members.clear();
}

void Guild::RefreshMemberNames() {
    std::lock_guard<std::mutex> lock(m_mutex);

    // 刷新所有在线成员的显示名称
    for (auto& member : m_members) {
        if (member.isOnline && member.playerObject) {
            member.playerObject->SendDefMessage(Protocol::SM_CHANGEGUILDNAME, 0, 0, 0, 0);
        }
    }
}

// ============================================================================
// GuildManager 类实现
// ============================================================================

GuildManager& GuildManager::GetInstance() {
    static GuildManager instance;
    return instance;
}

bool GuildManager::Initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return true;
    }

    // 设置配置路径
    m_guildListFile = "GuildList.txt";
    m_guildDir = "GuildBase/";

    // 创建行会目录
    std::filesystem::create_directories(m_guildDir);

    // 清理现有数据
    ClearGuildInfo();

    // 加载行会信息
    LoadGuildInfo();

    m_initialized = true;
    Logger::Info("GuildManager initialized successfully");
    return true;
}

void GuildManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    // 保存所有行会数据
    for (auto& guild : m_guilds) {
        if (guild) {
            guild->SaveToFile();
        }
    }

    // 保存行会列表
    SaveGuildList();

    // 清理数据
    ClearGuildInfo();

    m_initialized = false;
    Logger::Info("GuildManager finalized");
}

bool GuildManager::CreateGuild(const std::string& guildName, PlayObject* chief) {
    if (!chief || guildName.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查行会名称是否有效
    if (guildName.length() > 20 || guildName.length() < 2) {
        return false;
    }

    // 检查行会是否已存在
    if (FindGuild(guildName)) {
        return false;
    }

    // 检查玩家是否已经有行会
    if (GetPlayerGuild(chief->GetCharName())) {
        return false;
    }

    // 创建新行会
    auto guild = std::make_unique<Guild>(guildName);
    if (!guild->SetGuildInfo(chief->GetCharName())) {
        return false;
    }

    // 添加会长为成员
    if (!guild->AddMember(chief, GuildRank::CHIEF)) {
        return false;
    }

    // 添加到行会列表
    m_guilds.push_back(std::move(guild));

    // 保存行会列表
    SaveGuildList();

    Logger::Info("Guild created: " + guildName + " by " + chief->GetCharName());
    return true;
}

bool GuildManager::DeleteGuild(const std::string& guildName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guilds.begin(), m_guilds.end(),
        [&guildName](const std::unique_ptr<Guild>& guild) {
            return guild && guild->GetGuildName() == guildName;
        });

    if (it == m_guilds.end()) {
        return false;
    }

    // 检查是否只有会长一人
    if ((*it)->GetMemberCount() > 1) {
        return false;
    }

    // 备份行会文件
    (*it)->BackupGuildFile();

    // 移除行会
    m_guilds.erase(it);

    // 保存行会列表
    SaveGuildList();

    Logger::Info("Guild deleted: " + guildName);
    return true;
}

Guild* GuildManager::FindGuild(const std::string& guildName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = std::find_if(m_guilds.begin(), m_guilds.end(),
        [&guildName](const std::unique_ptr<Guild>& guild) {
            return guild && guild->GetGuildName() == guildName;
        });

    return (it != m_guilds.end()) ? it->get() : nullptr;
}

Guild* GuildManager::GetPlayerGuild(const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto& guild : m_guilds) {
        if (guild && guild->IsMember(playerName)) {
            return guild.get();
        }
    }

    return nullptr;
}

void GuildManager::LoadGuildInfo() {
    if (!std::filesystem::exists(m_guildListFile)) {
        Logger::Info("Guild list file not found, starting with empty guild list");
        return;
    }

    std::ifstream file(m_guildListFile);
    if (!file.is_open()) {
        Logger::Error("Failed to open guild list file: " + m_guildListFile);
        return;
    }

    std::string guildName;
    int loadedCount = 0;

    while (std::getline(file, guildName)) {
        guildName = Trim(guildName);
        if (guildName.empty()) {
            continue;
        }

        auto guild = std::make_unique<Guild>(guildName);
        if (guild->LoadFromFile()) {
            m_guilds.push_back(std::move(guild));
            loadedCount++;
        } else {
            Logger::Warning("Failed to load guild: " + guildName);
        }
    }

    file.close();
    Logger::Info("Loaded " + std::to_string(loadedCount) + " guilds");
}

void GuildManager::SaveGuildList() {
    std::ofstream file(m_guildListFile);
    if (!file.is_open()) {
        Logger::Error("Failed to save guild list file: " + m_guildListFile);
        return;
    }

    for (const auto& guild : m_guilds) {
        if (guild) {
            file << guild->GetGuildName() << std::endl;
        }
    }

    file.close();
    Logger::Debug("Guild list saved");
}

void GuildManager::Run() {
    if (!m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 运行所有行会的定时任务
    for (auto& guild : m_guilds) {
        if (guild) {
            guild->Run();
        }
    }
}

void GuildManager::ClearGuildInfo() {
    m_guilds.clear();
}

} // namespace MirServer
