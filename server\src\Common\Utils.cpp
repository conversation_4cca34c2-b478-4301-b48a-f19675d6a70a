#include "Utils.h"
#include <algorithm>
#include <cctype>
#include <chrono>
#include <random>
#include <sstream>
#include <iomanip>
#include <fstream>
#include <filesystem>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace MirServer {

// 时间相关工具函数
// GetCurrentTime已在Types.cpp中定义

std::string GetTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    char buffer[32];
    std::strftime(buffer, sizeof(buffer), "%H:%M:%S", &tm);
    return std::string(buffer);
}

std::string GetDateTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    char buffer[64];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &tm);
    return std::string(buffer);
}

// 随机数生成
// GenerateRandom已在Types.cpp中定义

float GenerateRandomFloat(float min, float max) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_real_distribution<> dis(min, max);
    return static_cast<float>(dis(gen));
}

bool GenerateRandomBool(float probability) {
    return GenerateRandomFloat(0.0f, 1.0f) < probability;
}

// 字符串工具函数
std::string Trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string ToUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::string ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::vector<std::string> Split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(str);

    while (std::getline(tokenStream, token, delimiter)) {
        tokens.push_back(token);
    }

    return tokens;
}

std::string Join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }

    std::string result = strings[0];
    for (size_t i = 1; i < strings.size(); ++i) {
        result += delimiter + strings[i];
    }

    return result;
}

bool StartsWith(const std::string& str, const std::string& prefix) {
    return str.length() >= prefix.length() &&
           str.compare(0, prefix.length(), prefix) == 0;
}

bool EndsWith(const std::string& str, const std::string& suffix) {
    return str.length() >= suffix.length() &&
           str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

// 数学工具函数
// GetDistance和IsInRange已在Types.cpp中定义

DirectionType GetDirection(const Point& from, const Point& to) {
    int dx = to.x - from.x;
    int dy = to.y - from.y;

    if (dx == 0 && dy == 0) return DirectionType::UP;

    // 计算角度并转换为方向
    double angle = std::atan2(dy, dx) * 180.0 / M_PI;
    if (angle < 0) angle += 360.0;

    // 将角度转换为8个方向
    int direction = static_cast<int>((angle + 22.5) / 45.0) % 8;
    return static_cast<DirectionType>(direction);
}

// GetDirectionFromPoints已在Types.cpp中定义

Point GetNextPosition(const Point& current, DirectionType direction) {
    Point next = current;

    switch (direction) {
        case DirectionType::UP:
            next.y--;
            break;
        case DirectionType::UP_RIGHT:
            next.x++;
            next.y--;
            break;
        case DirectionType::RIGHT:
            next.x++;
            break;
        case DirectionType::DOWN_RIGHT:
            next.x++;
            next.y++;
            break;
        case DirectionType::DOWN:
            next.y++;
            break;
        case DirectionType::DOWN_LEFT:
            next.x--;
            next.y++;
            break;
        case DirectionType::LEFT:
            next.x--;
            break;
        case DirectionType::UP_LEFT:
            next.x--;
            next.y--;
            break;
    }

    return next;
}

// 文件和路径工具
bool FileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

bool DirectoryExists(const std::string& dirname) {
    return std::filesystem::exists(dirname) && std::filesystem::is_directory(dirname);
}

bool CreateDirectory(const std::string& dirname) {
    return std::filesystem::create_directories(dirname);
}

std::string GetFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        return filename.substr(pos + 1);
    }
    return "";
}

std::string GetFileName(const std::string& filepath) {
    size_t pos = filepath.find_last_of("/\\");
    if (pos != std::string::npos) {
        return filepath.substr(pos + 1);
    }
    return filepath;
}

std::string GetDirectoryName(const std::string& filepath) {
    size_t pos = filepath.find_last_of("/\\");
    if (pos != std::string::npos) {
        return filepath.substr(0, pos);
    }
    return "";
}

// 数据转换
std::string IntToString(int value) {
    return std::to_string(value);
}

std::string FloatToString(float value, int precision) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(precision) << value;
    return oss.str();
}

int StringToInt(const std::string& str, int defaultValue) {
    try {
        return std::stoi(str);
    } catch (...) {
        return defaultValue;
    }
}

float StringToFloat(const std::string& str, float defaultValue) {
    try {
        return std::stof(str);
    } catch (...) {
        return defaultValue;
    }
}

bool StringToBool(const std::string& str, bool defaultValue) {
    std::string lower = ToLower(str);
    if (lower == "true" || lower == "1" || lower == "yes" || lower == "on") {
        return true;
    } else if (lower == "false" || lower == "0" || lower == "no" || lower == "off") {
        return false;
    }
    return defaultValue;
}

} // namespace MirServer