// GameEngine.cpp - 游戏引擎实现
#include "GameEngine.h"
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "NPCManager.h"
#include "MonsterManager.h"
#include "Environment.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "PKManager.h"
#include "GroupManager.h"
#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "../BaseObject/Monster.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include <fstream>
#include <sstream>
#include <chrono>
#include <thread>

namespace MirServer {

GameEngine::GameEngine() {
    m_state = GameEngineState::STOPPED;
    m_initialized = false;
    m_debugMode = false;
    m_running = false;
    m_lastTickTime = GetCurrentTime();
    m_lastSaveTime = GetCurrentTime();
    m_lastCleanupTime = GetCurrentTime();
    m_lastStatsTime = GetCurrentTime();
    m_autoSaveEnabled = true;
    m_autoSaveInterval = 300000; // 5分钟

    // 初始化统计信息
    memset(&m_stats, 0, sizeof(m_stats));
}

GameEngine::~GameEngine() {
    Finalize();
}

bool GameEngine::Initialize() {
    if (m_initialized) {
        return true;
    }

    Logger::Info("Initializing GameEngine...");
    SetState(GameEngineState::STARTING);

    // 加载配置
    if (!LoadConfig()) {
        Logger::Warning("Failed to load config, using defaults");
    }

    // 初始化管理器
    if (!InitializeManagers()) {
        Logger::Error("Failed to initialize managers");
        SetState(GameEngineState::ERROR_STATE);
        return false;
    }

    // 初始化数据
    if (!InitializeData()) {
        Logger::Error("Failed to initialize data");
        SetState(GameEngineState::ERROR_STATE);
        return false;
    }

    // 初始化网络
    if (!InitializeNetwork()) {
        Logger::Error("Failed to initialize network");
        SetState(GameEngineState::ERROR_STATE);
        return false;
    }

    m_initialized = true;
    m_stats.startTime = GetCurrentTime();

    Logger::Info("GameEngine initialized successfully");
    return true;
}

bool GameEngine::Initialize(const GameEngineConfig& config) {
    m_config = config;
    return Initialize();
}

void GameEngine::Finalize() {
    if (!m_initialized) {
        return;
    }

    Logger::Info("Finalizing GameEngine...");
    SetState(GameEngineState::STOPPING);

    // 停止运行
    Stop();

    // 保存所有数据
    SaveAll();

    // 清理网络
    FinalizeNetwork();

    // 清理数据
    FinalizeData();

    // 清理管理器
    FinalizeManagers();

    m_initialized = false;
    SetState(GameEngineState::STOPPED);

    Logger::Info("GameEngine finalized");
}

bool GameEngine::Start() {
    if (!m_initialized) {
        Logger::Error("GameEngine not initialized");
        return false;
    }

    if (m_running) {
        Logger::Warning("GameEngine already running");
        return true;
    }

    Logger::Info("Starting GameEngine...");
    SetState(GameEngineState::STARTING);

    m_running = true;

    // 启动游戏线程
    m_gameThread = std::thread([this]() {
        Run();
    });

    SetState(GameEngineState::RUNNING);
    Logger::Info("GameEngine started successfully");

    return true;
}

void GameEngine::Stop() {
    if (!m_running) {
        return;
    }

    Logger::Info("Stopping GameEngine...");
    SetState(GameEngineState::STOPPING);

    m_running = false;

    // 等待游戏线程结束
    if (m_gameThread.joinable()) {
        m_gameThread.join();
    }

    SetState(GameEngineState::STOPPED);
    Logger::Info("GameEngine stopped");
}

bool GameEngine::InitializeManagers() {
    try {
        // 创建管理器实例
        m_userEngine = std::make_unique<UserEngine>();
        m_mapManager = std::make_unique<MapManager>();
        m_itemManager = std::make_unique<ItemManager>();
        m_magicManager = std::make_unique<MagicManager>();
        m_npcManager = std::make_unique<NPCManager>();
        m_monsterManager = std::make_unique<MonsterManager>();
        // m_environmentManager = std::make_unique<EnvironmentManager>();

        // 创建新的核心功能管理器
        m_storageManager = std::make_unique<StorageManager>();
        m_tradeManager = std::make_unique<TradeManager>();
        m_questManager = std::make_unique<QuestManager>();
        m_miniMapManager = std::make_unique<MiniMapManager>();
        m_repairManager = std::make_unique<RepairManager>();

        // 初始化管理器
        if (!m_userEngine->Initialize(std::shared_ptr<MapManager>(m_mapManager.get(), [](MapManager*){}),
                                       std::shared_ptr<ItemManager>(m_itemManager.get(), [](ItemManager*){}))) {
            Logger::Error("Failed to initialize UserEngine");
            return false;
        }

        if (!m_mapManager->Initialize(m_config.mapPath)) {
            Logger::Error("Failed to initialize MapManager");
            return false;
        }

        if (!m_itemManager->Initialize(m_config.dataPath)) {
            Logger::Error("Failed to initialize ItemManager");
            return false;
        }

        if (!m_magicManager->Initialize()) {
            Logger::Error("Failed to initialize MagicManager");
            return false;
        }

        if (!m_npcManager->Initialize()) {
            Logger::Error("Failed to initialize NPCManager");
            return false;
        }

        if (!m_monsterManager->Initialize()) {
            Logger::Error("Failed to initialize MonsterManager");
            return false;
        }

        // 初始化新的核心功能管理器
        if (!m_storageManager->Initialize()) {
            Logger::Error("Failed to initialize StorageManager");
            return false;
        }

        if (!m_tradeManager->Initialize()) {
            Logger::Error("Failed to initialize TradeManager");
            return false;
        }

        if (!m_questManager->Initialize()) {
            Logger::Error("Failed to initialize QuestManager");
            return false;
        }

        if (!m_miniMapManager->Initialize()) {
            Logger::Error("Failed to initialize MiniMapManager");
            return false;
        }

        if (!m_repairManager->Initialize()) {
            Logger::Error("Failed to initialize RepairManager");
            return false;
        }

        // 初始化PK管理器
        PKManager::GetInstance().Initialize();

        // 初始化组队管理器
        GroupManager::GetInstance().Initialize();

        // 初始化行会管理器
        GuildManager::GetInstance().Initialize();

        Logger::Info("All managers initialized successfully");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("Exception during manager initialization: " + std::string(e.what()));
        return false;
    }
}

bool GameEngine::InitializeData() {
    // 数据初始化在各个管理器的Initialize中完成
    Logger::Info("Data initialization completed");
    return true;
}

bool GameEngine::InitializeNetwork() {
    // 网络初始化（如果需要的话）
    Logger::Info("Network initialization completed");
    return true;
}

void GameEngine::FinalizeManagers() {
    // 清理行会管理器
    GuildManager::GetInstance().Finalize();

    // 清理组队管理器
    GroupManager::GetInstance().Finalize();

    // 清理PK管理器
    PKManager::GetInstance().Finalize();

    // 先清理新的核心功能管理器
    if (m_repairManager) {
        m_repairManager->Finalize();
        m_repairManager.reset();
    }

    if (m_miniMapManager) {
        m_miniMapManager->Finalize();
        m_miniMapManager.reset();
    }

    if (m_questManager) {
        m_questManager->Finalize();
        m_questManager.reset();
    }

    if (m_tradeManager) {
        m_tradeManager->Finalize();
        m_tradeManager.reset();
    }

    if (m_storageManager) {
        m_storageManager->Finalize();
        m_storageManager.reset();
    }

    // 清理原有管理器
    if (m_monsterManager) {
        m_monsterManager->Finalize();
        m_monsterManager.reset();
    }

    if (m_npcManager) {
        m_npcManager->Finalize();
        m_npcManager.reset();
    }

    if (m_magicManager) {
        m_magicManager->Finalize();
        m_magicManager.reset();
    }

    if (m_itemManager) {
        m_itemManager->Finalize();
        m_itemManager.reset();
    }

    if (m_mapManager) {
        m_mapManager->Finalize();
        m_mapManager.reset();
    }

    if (m_userEngine) {
        m_userEngine->Finalize();
        m_userEngine.reset();
    }

    Logger::Info("All managers finalized");
}

void GameEngine::FinalizeData() {
    // 清理玩家数据
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players.clear();
        m_playersByName.clear();
    }

    Logger::Info("Data finalization completed");
}

void GameEngine::FinalizeNetwork() {
    // 网络清理
    Logger::Info("Network finalization completed");
}

void GameEngine::Run() {
    Logger::Info("GameEngine main loop started");

    const DWORD targetTickTime = 50; // 20 FPS

    while (m_running) {
        DWORD tickStart = GetCurrentTime();

        try {
            // 处理单次tick
            Tick();

            // 计算tick时间
            DWORD tickTime = GetCurrentTime() - tickStart;

            // 更新统计信息
            {
                std::lock_guard<std::mutex> lock(m_statsMutex);
                m_stats.tickCount++;
                m_stats.averageTickTime = (m_stats.averageTickTime * (m_stats.tickCount - 1) + tickTime) / m_stats.tickCount;
                if (tickTime > m_stats.maxTickTime) {
                    m_stats.maxTickTime = tickTime;
                }
            }

            // 控制帧率
            if (tickTime < targetTickTime) {
                std::this_thread::sleep_for(std::chrono::milliseconds(targetTickTime - tickTime));
            }

        } catch (const std::exception& e) {
            Logger::Error("Exception in game loop: " + std::string(e.what()));
            // 继续运行，不要因为单个异常而停止整个引擎
        }
    }

    Logger::Info("GameEngine main loop ended");
}

void GameEngine::Tick() {
    DWORD currentTime = GetCurrentTime();

    // 处理游戏逻辑
    ProcessGameLogic();

    // 处理网络
    ProcessNetworking();

    // 处理自动保存
    if (m_autoSaveEnabled && currentTime - m_lastSaveTime >= m_autoSaveInterval) {
        ProcessAutoSave();
        m_lastSaveTime = currentTime;
    }

    // 处理清理
    if (currentTime - m_lastCleanupTime >= 60000) { // 每分钟清理一次
        ProcessCleanup();
        m_lastCleanupTime = currentTime;
    }

    // 处理统计信息
    if (currentTime - m_lastStatsTime >= 10000) { // 每10秒更新一次统计
        ProcessStatistics();
        m_lastStatsTime = currentTime;
    }

    m_lastTickTime = currentTime;
}

void GameEngine::ProcessGameLogic() {
    // 处理用户引擎
    if (m_userEngine) {
        m_userEngine->Run();
    }

    // 处理魔法管理器
    if (m_magicManager) {
        m_magicManager->Run();
    }

    // 处理NPC管理器
    if (m_npcManager) {
        m_npcManager->Run();
    }

    // 处理怪物管理器
    if (m_monsterManager) {
        m_monsterManager->Run();
    }

    // 处理新的核心功能管理器
    if (m_storageManager) {
        m_storageManager->CleanupExpiredSessions();
    }

    if (m_tradeManager) {
        m_tradeManager->Run();
    }

    if (m_questManager) {
        m_questManager->Run();
    }

    if (m_miniMapManager) {
        m_miniMapManager->Run();
    }

    // 更新PK管理器
    PKManager::GetInstance().Update();

    // 更新组队管理器
    GroupManager::GetInstance().Run();

    // 更新行会管理器
    GuildManager::GetInstance().Run();

    // 物品管理器不需要Run方法
}

void GameEngine::ProcessNetworking() {
    // 网络处理（如果需要的话）
    // 这里可以处理网络消息队列等
}

void GameEngine::ProcessCleanup() {
    // 清理过期的对象和数据
    Logger::Debug("Processing cleanup...");

    // 清理死亡的怪物
    if (m_monsterManager) {
        // TODO: 实现怪物清理
    }

    // 清理过期的物品
    if (m_itemManager) {
        // TODO: 实现物品清理
    }
}

void GameEngine::ProcessAutoSave() {
    Logger::Info("Auto-saving game data...");
    SaveAll();
}

void GameEngine::ProcessStatistics() {
    UpdateStatistics();
}

bool GameEngine::LoadConfig(const std::string& configFile) {
    try {
        return ParseConfigFile(configFile);
    } catch (const std::exception& e) {
        Logger::Error("Error loading config: " + std::string(e.what()));
        return false;
    }
}

bool GameEngine::SaveConfig(const std::string& configFile) {
    try {
        return WriteConfigFile(configFile);
    } catch (const std::exception& e) {
        Logger::Error("Error saving config: " + std::string(e.what()));
        return false;
    }
}

bool GameEngine::ParseConfigFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        Logger::Warning("Config file not found: " + filename);
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释和空行
        if (line.empty() || line[0] == '#' || line[0] == ';') {
            continue;
        }

        // 解析键值对
        size_t pos = line.find('=');
        if (pos != std::string::npos) {
            std::string key = line.substr(0, pos);
            std::string value = line.substr(pos + 1);

            // 去除空格
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            // 设置配置值
            if (key == "ServerName") {
                m_config.serverName = value;
            } else if (key == "ServerPort") {
                m_config.serverPort = static_cast<WORD>(std::stoi(value));
            } else if (key == "MaxPlayers") {
                m_config.maxPlayers = std::stoi(value);
            } else if (key == "ExpRate") {
                m_config.expRate = std::stof(value);
            } else if (key == "DropRate") {
                m_config.dropRate = std::stof(value);
            } else if (key == "GoldRate") {
                m_config.goldRate = std::stof(value);
            } else if (key == "PKEnabled") {
                m_config.pkEnabled = (value == "1" || value == "true");
            } else if (key == "GuildEnabled") {
                m_config.guildEnabled = (value == "1" || value == "true");
            } else if (key == "TradeEnabled") {
                m_config.tradeEnabled = (value == "1" || value == "true");
            } else if (key == "MaxMonstersPerMap") {
                m_config.maxMonstersPerMap = std::stoi(value);
            } else if (key == "MaxItemsPerMap") {
                m_config.maxItemsPerMap = std::stoi(value);
            } else if (key == "SaveInterval") {
                m_config.saveInterval = static_cast<DWORD>(std::stoi(value));
            } else if (key == "CleanupInterval") {
                m_config.cleanupInterval = static_cast<DWORD>(std::stoi(value));
            } else if (key == "DataPath") {
                m_config.dataPath = value;
            } else if (key == "MapPath") {
                m_config.mapPath = value;
            } else if (key == "LogPath") {
                m_config.logPath = value;
            } else if (key == "ConfigPath") {
                m_config.configPath = value;
            }
        }
    }

    file.close();
    Logger::Info("Config loaded from: " + filename);
    return true;
}

bool GameEngine::WriteConfigFile(const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        Logger::Error("Cannot create config file: " + filename);
        return false;
    }

    file << "# GameEngine Configuration File\n";
    file << "# Generated automatically\n\n";

    file << "[Server]\n";
    file << "ServerName=" << m_config.serverName << "\n";
    file << "ServerPort=" << m_config.serverPort << "\n";
    file << "MaxPlayers=" << m_config.maxPlayers << "\n\n";

    file << "[Game]\n";
    file << "ExpRate=" << m_config.expRate << "\n";
    file << "DropRate=" << m_config.dropRate << "\n";
    file << "GoldRate=" << m_config.goldRate << "\n";
    file << "PKEnabled=" << (m_config.pkEnabled ? "1" : "0") << "\n";
    file << "GuildEnabled=" << (m_config.guildEnabled ? "1" : "0") << "\n";
    file << "TradeEnabled=" << (m_config.tradeEnabled ? "1" : "0") << "\n\n";

    file << "[Performance]\n";
    file << "MaxMonstersPerMap=" << m_config.maxMonstersPerMap << "\n";
    file << "MaxItemsPerMap=" << m_config.maxItemsPerMap << "\n";
    file << "SaveInterval=" << m_config.saveInterval << "\n";
    file << "CleanupInterval=" << m_config.cleanupInterval << "\n\n";

    file << "[Paths]\n";
    file << "DataPath=" << m_config.dataPath << "\n";
    file << "MapPath=" << m_config.mapPath << "\n";
    file << "LogPath=" << m_config.logPath << "\n";
    file << "ConfigPath=" << m_config.configPath << "\n";

    file.close();
    Logger::Info("Config saved to: " + filename);
    return true;
}

bool GameEngine::AddPlayer(std::shared_ptr<PlayObject> player) {
    if (!player) return false;

    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    uint32_t playerId = player->GetObjectId();
    std::string playerName = player->GetCharName();

    // 检查是否已存在
    if (m_players.find(playerId) != m_players.end()) {
        return false;
    }

    if (m_playersByName.find(playerName) != m_playersByName.end()) {
        return false;
    }

    // 检查玩家数量限制
    if (static_cast<int>(m_players.size()) >= m_config.maxPlayers) {
        return false;
    }

    // 添加玩家
    m_players[playerId] = player;
    m_playersByName[playerName] = player;

    // 触发登录事件
    OnPlayerLogin(player);

    return true;
}

bool GameEngine::RemovePlayer(uint32_t playerId) {
    std::unique_lock<std::shared_mutex> lock(m_playersMutex);

    auto it = m_players.find(playerId);
    if (it != m_players.end()) {
        auto player = it->second;

        // 从名称映射中移除
        if (player) {
            auto nameIt = m_playersByName.find(player->GetCharName());
            if (nameIt != m_playersByName.end()) {
                m_playersByName.erase(nameIt);
            }

            // 触发登出事件
            OnPlayerLogout(player);
        }

        // 从主映射中移除
        m_players.erase(it);

        return true;
    }

    return false;
}

std::shared_ptr<PlayObject> GameEngine::FindPlayer(uint32_t playerId) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    auto it = m_players.find(playerId);
    return (it != m_players.end()) ? it->second : nullptr;
}

std::shared_ptr<PlayObject> GameEngine::FindPlayerByName(const std::string& playerName) const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    auto it = m_playersByName.find(playerName);
    return (it != m_playersByName.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<PlayObject>> GameEngine::GetAllPlayers() const {
    std::vector<std::shared_ptr<PlayObject>> result;
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);

    result.reserve(m_players.size());
    for (const auto& pair : m_players) {
        if (pair.second) {
            result.push_back(pair.second);
        }
    }

    return result;
}

int GameEngine::GetOnlinePlayerCount() const {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    return static_cast<int>(m_players.size());
}

void GameEngine::OnPlayerLogin(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    Logger::Info("Player logged in: " + player->GetCharName());

    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_stats.totalLogins++;
        m_stats.onlinePlayers = GetOnlinePlayerCount();
        if (m_stats.onlinePlayers > m_stats.maxOnlinePlayers) {
            m_stats.maxOnlinePlayers = m_stats.onlinePlayers;
        }
    }
}

void GameEngine::OnPlayerLogout(std::shared_ptr<PlayObject> player) {
    if (!player) return;

    Logger::Info("Player logged out: " + player->GetCharName());

    // 更新统计信息
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_stats.onlinePlayers = GetOnlinePlayerCount();
    }
}

void GameEngine::OnPlayerDeath(std::shared_ptr<PlayObject> player, BaseObject* killer) {
    if (!player) return;

    Logger::Info("Player died: " + player->GetCharName());

    // TODO: 处理玩家死亡逻辑
}

void GameEngine::OnMonsterDeath(std::shared_ptr<Monster> monster, BaseObject* killer) {
    if (!monster) return;

    Logger::Debug("Monster died: " + monster->GetCharName());

    // TODO: 处理怪物死亡逻辑
}

void GameEngine::OnItemDrop(std::shared_ptr<class MapItem> item) {
    if (!item) return;

    Logger::Debug("Item dropped");

    // TODO: 处理物品掉落逻辑
}

bool GameEngine::SaveAll() {
    Logger::Info("Saving all game data...");

    bool success = true;

    // 保存玩家数据
    if (m_userEngine) {
        m_userEngine->SaveAllPlayers();
        Logger::Info("Player data saved");
    }

    // 保存其他数据...

    if (success) {
        Logger::Info("All game data saved successfully");
    } else {
        Logger::Error("Some data failed to save");
    }

    return success;
}

bool GameEngine::LoadAll() {
    Logger::Info("Loading all game data...");

    bool success = true;

    // 加载数据在Initialize中完成

    if (success) {
        Logger::Info("All game data loaded successfully");
    } else {
        Logger::Error("Some data failed to load");
    }

    return success;
}

void GameEngine::SetAutoSave(bool enabled, DWORD interval) {
    m_autoSaveEnabled = enabled;
    m_autoSaveInterval = interval;

    Logger::Info("Auto-save " + std::string(enabled ? "enabled" : "disabled") +
                 " with interval: " + std::to_string(interval) + "ms");
}

void GameEngine::UpdateStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);

    DWORD currentTime = GetCurrentTime();
    m_stats.runTime = currentTime - m_stats.startTime;
    m_stats.onlinePlayers = GetOnlinePlayerCount();

    // 更新对象统计
    if (m_npcManager) {
        m_stats.totalNPCs = m_npcManager->GetStatistics().totalNPCs;
    }

    if (m_monsterManager) {
        m_stats.totalMonsters = m_monsterManager->GetStatistics().totalMonsters;
    }

    if (m_itemManager) {
        // m_stats.totalItems = m_itemManager->GetStatistics().totalItems;
    }

    if (m_mapManager) {
        // m_stats.totalMaps = m_mapManager->GetLoadedMapCount();
    }

    m_stats.lastUpdateTime = currentTime;
}

void GameEngine::ResetStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    memset(&m_stats, 0, sizeof(m_stats));
    m_stats.startTime = GetCurrentTime();
}

void GameEngine::SetState(GameEngineState state) {
    m_state = state;
}

void GameEngine::HandleError(const std::string& error) {
    Logger::Error("GameEngine error: " + error);
    SetState(GameEngineState::ERROR_STATE);
}

void GameEngine::BroadcastMessage(const std::string& message) {
    auto players = GetAllPlayers();
    for (auto player : players) {
        if (player) {
            // TODO: 发送消息给玩家
            // player->SendMessage(message);
        }
    }
}

void GameEngine::BroadcastSystemMessage(const std::string& message) {
    Logger::Info("System message: " + message);
    BroadcastMessage("[系统] " + message);
}

void GameEngine::DumpStatus() const {
    Logger::Info("=== GameEngine Status ===");
    Logger::Info("State: " + std::to_string(static_cast<int>(m_state.load())));
    Logger::Info("Running: " + std::string(m_running ? "Yes" : "No"));
    Logger::Info("Online Players: " + std::to_string(GetOnlinePlayerCount()));
    Logger::Info("Run Time: " + std::to_string(m_stats.runTime) + "ms");
    Logger::Info("Average Tick Time: " + std::to_string(m_stats.averageTickTime) + "ms");
    Logger::Info("Max Tick Time: " + std::to_string(m_stats.maxTickTime) + "ms");
    Logger::Info("========================");
}

void GameEngine::DumpMemoryUsage() const {
    // TODO: 实现内存使用统计
    Logger::Info("Memory usage information not implemented yet");
}

PKManager* GameEngine::GetPKManager() const {
    return &PKManager::GetInstance();
}

GroupManager* GameEngine::GetGroupManager() const {
    return &GroupManager::GetInstance();
}

GuildManager* GameEngine::GetGuildManager() const {
    return &GuildManager::GetInstance();
}

} // namespace MirServer