// NPC.cpp - NPC对象实现
#include "NPC.h"
#include "PlayObject.h"
#include "../Protocol/PacketTypes.h"
#include "../Common/Types.h"
#include "../GameEngine/ScriptEngine.h"
#include "../Common/Logger.h"
#include <sstream>
#include <algorithm>
#include <fstream>

namespace MirServer {
using namespace Protocol;  // 添加Protocol命名空间

// 静态成员初始化
std::shared_ptr<ScriptEngine> NPC::s_scriptEngine = nullptr;

// ==================== NPC基类实现 ====================

NPC::NPC() : BaseObject() {
    // NPC默认不会死亡
    m_maxHP = 9999;
    m_hp = 9999;

    // NPC默认属性
    m_state = ObjectState::NORMAL;
    m_light = 1; // NPC有一定光源
    m_viewRange = 6; // NPC视野范围较小
}

NPC::~NPC() {
    ClearDialogs();
}

void NPC::Initialize() {
    // 初始化脚本引擎（全局单例）
    if (!s_scriptEngine) {
        s_scriptEngine = std::make_shared<ScriptEngine>();
    }

    // 加载脚本
    if (!m_scriptFile.empty()) {
        LoadScript();
    }

    m_lastMoveTime = GetCurrentTime();
}

void NPC::Finalize() {
    // 清理对话状态
    m_playerDialogStates.clear();
    m_playerScriptStates.clear();

    // 清理脚本
    m_script.reset();
}

void NPC::Run() {
    // 处理AI
    ProcessAI();
}

void NPC::ProcessAI() {
    if (!m_canMove) return;

    DWORD currentTime = GetCurrentTime();
    if (currentTime - m_lastMoveTime >= m_moveInterval) {
        RandomMove();
        m_lastMoveTime = currentTime;
    }
}

void NPC::RandomMove() {
    // 随机选择一个方向移动
    DirectionType newDir = static_cast<DirectionType>(GenerateRandom(0, 7));
    Point nextPos = GetNextPosition(newDir);

    if (BaseObject::CanMove(nextPos)) {  // 调用基类的CanMove
        TurnTo(newDir);
        m_currentPos = nextPos;
        OnPositionChanged();

        // 发送移动消息
        SendDefMessage(Protocol::SM_WALK, 0, nextPos.x, nextPos.y, static_cast<WORD>(newDir));
    }
}

void NPC::OnClick(PlayObject* player) {
    if (!player) return;

    // 检查玩家是否在范围内
    if (!CheckPlayerInRange(player)) {
        player->SendMessage("距离太远了！");
        return;
    }

    // 面向玩家
    DirectionType dir = GetDirectionFromPoints(m_currentPos, player->GetCurrentPos());
    TurnTo(dir);

    // 优先使用脚本执行引擎
    if (HasScript()) {
        ExecuteNPCScript(player, "main");
    } else {
        // 回退到传统对话系统
        ProcessDialog(player, 0); // 从第一个对话开始
    }
}

void NPC::OnPlayerTalk(PlayObject* player) {
    if (!player) return;

    // 默认实现：开始对话
    OnClick(player);
}

void NPC::ProcessDialog(PlayObject* player, int dialogId, int optionId) {
    if (!player) return;

    // 如果有选项ID，处理选项跳转
    if (optionId >= 0) {
        const NPCDialog* currentDialog = GetDialog(dialogId);
        if (currentDialog) {
            auto it = currentDialog->optionGotos.find(optionId);
            if (it != currentDialog->optionGotos.end()) {
                dialogId = it->second;
            }
        }
    }

    // 保存玩家当前对话状态
    m_playerDialogStates[player] = dialogId;

    // 发送对话内容
    SendDialogToPlayer(player, dialogId);

    // 执行脚本
    const NPCDialog* dialog = GetDialog(dialogId);
    if (dialog && !dialog->script.empty()) {
        ExecuteScript(player, dialog->script);
    }
}

void NPC::SendDialogToPlayer(PlayObject* player, int dialogId) {
    const NPCDialog* dialog = GetDialog(dialogId);
    if (!dialog) return;

    // 替换对话中的变量
    std::string text = ReplaceDialogVariables(dialog->text, player);

    // TODO: 发送对话内容到客户端
    player->SendDefMessage(Protocol::SM_MERCHANTDLG, dialogId, 0, 0, 0);
}

void NPC::AddDialog(int id, const NPCDialog& dialog) {
    m_dialogs[id] = dialog;
}

const NPCDialog* NPC::GetDialog(int id) const {
    auto it = m_dialogs.find(id);
    return (it != m_dialogs.end()) ? &it->second : nullptr;
}

void NPC::ClearDialogs() {
    m_dialogs.clear();
}

void NPC::LoadScript() {
    if (m_scriptFile.empty()) {
        Logger::Warning("NPC " + m_charName + " 没有设置脚本文件");
        return;
    }

    // 创建脚本对象
    m_script = std::make_unique<NPCScript>(m_charName, m_scriptFile);

    // 使用脚本解析器加载脚本
    ScriptParser parser;
    if (!parser.ParseScriptFile(m_scriptFile, *m_script)) {
        Logger::Error("加载NPC脚本失败: " + m_scriptFile + " - " + parser.GetLastError());
        m_script.reset();

        // 回退到默认对话
        LoadDefaultDialog();
        return;
    }

    Logger::Info("成功加载NPC脚本: " + m_scriptFile + " (共 " + std::to_string(m_script->blocks.size()) + " 个脚本块)");
}

void NPC::LoadDefaultDialog() {
    // 示例：添加默认对话
    NPCDialog defaultDialog;
    defaultDialog.text = "你好，<$USERNAME>！我是" + m_charName + "。";
    defaultDialog.options = {"交易", "任务", "离开"};
    defaultDialog.optionGotos[0] = 1; // 交易跳转到对话1
    defaultDialog.optionGotos[1] = 2; // 任务跳转到对话2
    defaultDialog.optionGotos[2] = -1; // 离开结束对话

    AddDialog(0, defaultDialog);
}

bool NPC::CheckPlayerInRange(PlayObject* player, int range) const {
    if (!player) return false;

    Point playerPos = player->GetCurrentPos();
    return IsInRange(m_currentPos, playerPos, range);
}

bool NPC::ExecuteNPCScript(PlayObject* player, const std::string& label) {
    if (!player || !HasScript() || !s_scriptEngine) {
        return false;
    }

    // 执行脚本
    bool result = s_scriptEngine->ExecuteScript(player, this, *m_script, label);

    if (!result) {
        Logger::Warning("NPC脚本执行失败: " + m_charName + " - " + s_scriptEngine->GetLastError());
    }

    return result;
}

void NPC::OnScriptDialog(PlayObject* player, int optionIndex) {
    if (!player || !HasScript()) {
        return;
    }

    // 根据选项索引跳转到对应的脚本标签
    // TODO: 实现选项处理逻辑
    // 这里需要根据当前脚本块的选项配置来决定跳转到哪个标签

    Logger::Info("玩家 " + player->GetCharName() + " 选择了选项 " + std::to_string(optionIndex));
}

void NPC::ExecuteScript(PlayObject* player, const std::string& script) {
    // 传统脚本执行方法（保持兼容性）
    // TODO: 执行脚本命令
    // 例如：给予物品、检查任务、传送等
}

std::string NPC::ReplaceDialogVariables(const std::string& text, PlayObject* player) const {
    std::string result = text;

    if (player) {
        // 替换玩家名称
        size_t pos = result.find("<$USERNAME>");
        if (pos != std::string::npos) {
            result.replace(pos, 11, player->GetCharName());
        }

        // 替换等级
        pos = result.find("<$LEVEL>");
        if (pos != std::string::npos) {
            result.replace(pos, 8, std::to_string(player->GetAbility().level));
        }

        // TODO: 添加更多变量替换
    }

    return result;
}

void NPC::Say(const std::string& msg) {
    // NPC公开说话
    SendDefMessage(Protocol::SM_MONSTERSAY, 0, 0, 0, 0);
}

void NPC::SayTo(PlayObject* player, const std::string& msg) {
    if (!player) return;

    // NPC对特定玩家说话
    player->SendMessage(m_charName + ": " + msg);
}

// ==================== 商人NPC实现 ====================

Merchant::Merchant() : NPC() {
    SetNPCType(NPCType::MERCHANT);
}

Merchant::~Merchant() {
    m_shopItems.clear();
}

void Merchant::Initialize() {
    NPC::Initialize();

    // 加载商店数据
    LoadShopData();
}

void Merchant::LoadShopData() {
    // TODO: 从文件或数据库加载商品数据

    // 示例：添加一些默认商品
    ShopItem item1;
    item1.itemIndex = 1; // 红药水
    item1.itemName = "红药水";
    item1.price = 100;
    item1.stock = -1; // 无限库存
    AddShopItem(item1);

    ShopItem item2;
    item2.itemIndex = 2; // 蓝药水
    item2.itemName = "蓝药水";
    item2.price = 200;
    item2.stock = -1;
    AddShopItem(item2);
}

void Merchant::OpenShop(PlayObject* player) {
    if (!player) return;

    // 检查距离
    if (!CheckPlayerInRange(player)) {
        player->SendMessage("距离太远了！");
        return;
    }

    // 发送商品列表
    player->SendDefMessage(Protocol::SM_SENDGOODSLIST, 0, 0, 0, 0);
}

void Merchant::AddShopItem(const ShopItem& item) {
    m_shopItems.push_back(item);
}

bool Merchant::AddShopItem(WORD itemIndex, int price) {
    ShopItem item;
    item.itemIndex = itemIndex;
    item.itemName = "Item_" + std::to_string(itemIndex); // 临时名称
    item.price = (price > 0) ? price : 100; // 默认价格
    item.stock = -1; // 无限库存

    AddShopItem(item);
    return true;
}

void Merchant::RemoveShopItem(WORD itemIndex) {
    m_shopItems.erase(
        std::remove_if(m_shopItems.begin(), m_shopItems.end(),
                      [itemIndex](const ShopItem& item) {
                          return item.itemIndex == itemIndex;
                      }),
        m_shopItems.end()
    );
}

void Merchant::UpdateItemPrice(WORD itemIndex, DWORD newPrice) {
    for (auto& item : m_shopItems) {
        if (item.itemIndex == itemIndex) {
            item.price = newPrice;
            break;
        }
    }
}

void Merchant::UpdateItemStock(WORD itemIndex, int stock) {
    for (auto& item : m_shopItems) {
        if (item.itemIndex == itemIndex) {
            item.stock = stock;
            break;
        }
    }
}

bool Merchant::SellItemToPlayer(PlayObject* player, WORD itemIndex, int count) {
    if (!player || count <= 0) return false;

    // 查找商品
    ShopItem* shopItem = nullptr;
    for (auto& item : m_shopItems) {
        if (item.itemIndex == itemIndex) {
            shopItem = &item;
            break;
        }
    }

    if (!shopItem) {
        player->SendMessage("没有这个商品！");
        return false;
    }

    // 检查库存
    if (shopItem->stock != -1 && shopItem->stock < count) {
        player->SendMessage("库存不足！");
        return false;
    }

    // 计算总价
    DWORD totalPrice = shopItem->price * count;

    // 检查玩家金币
    if (player->GetGold() < totalPrice) {
        player->SendMessage("金币不足！");
        return false;
    }

    // 检查背包空间
    if (player->IsBagFull()) {
        player->SendMessage("背包已满！");
        return false;
    }

    // 扣除金币
    if (!player->DecGold(totalPrice)) {
        return false;
    }

    // 给予物品
    for (int i = 0; i < count; i++) {
        UserItem newItem;
        newItem.itemIndex = itemIndex;
        newItem.itemName = shopItem->itemName;
        newItem.makeIndex = static_cast<WORD>(GetCurrentTime() + i); // 临时生成索引
        newItem.dura = 100;
        newItem.duraMax = 100;

        if (!player->AddBagItem(newItem)) {
            // 如果添加失败，退还金币
            player->IncGold(shopItem->price * (count - i));
            break;
        }
    }

    // 更新库存
    if (shopItem->stock != -1) {
        shopItem->stock -= count;
    }

    // 发送成功消息
    player->SendDefMessage(Protocol::SM_BUYITEM_SUCCESS, 0, 0, 0, 0);

    return true;
}

bool Merchant::BuyItemFromPlayer(PlayObject* player, WORD makeIndex) {
    if (!player) return false;

    // TODO: 实现从玩家购买物品

    return false;
}

DWORD Merchant::GetBuyPrice(const UserItem& item) const {
    // TODO: 根据物品计算收购价格
    return static_cast<DWORD>(100 * m_sellPriceRate);
}

DWORD Merchant::GetRepairCost(const UserItem& item) const {
    if (!m_canRepair) return 0;

    // 修理费用计算：基础价格 * 损坏程度
    if (item.duraMax == 0) return 0;

    float damageRate = 1.0f - (static_cast<float>(item.dura) / item.duraMax);
    DWORD baseCost = 100; // TODO: 根据物品等级计算基础费用

    return static_cast<DWORD>(baseCost * damageRate);
}

bool Merchant::RepairItem(PlayObject* player, WORD makeIndex) {
    if (!player || !m_canRepair) return false;

    // TODO: 实现修理物品

    return false;
}

// ==================== 守卫NPC实现 ====================

Guard::Guard() : NPC() {
    SetNPCType(NPCType::GUARD);

    // 守卫属性
    m_maxHP = 10000;
    m_hp = 10000;
    m_viewRange = 15; // 守卫视野更大
}

Guard::~Guard() {
}

void Guard::Initialize() {
    NPC::Initialize();

    // 守卫可以移动
    SetCanMove(true);
    SetMoveInterval(3000); // 3秒巡逻一次
}

void Guard::Run() {
    NPC::Run();

    // 检查并攻击犯罪者
    CheckAndAttackCriminals();
}

void Guard::ProcessAI() {
    // 如果有目标，追击目标
    if (m_currentTarget && m_currentTarget->IsAlive()) {
        // TODO: 实现追击逻辑
    } else {
        // 否则巡逻
        NPC::ProcessAI();
    }
}

void Guard::CheckAndAttackCriminals() {
    if (!m_attackCriminals) return;

    // 获取视野内的对象
    std::vector<BaseObject*> viewObjects;
    GetViewObjects(viewObjects);

    for (BaseObject* obj : viewObjects) {
        if (obj->GetObjectType() == ObjectType::HUMAN) {
            PlayObject* player = static_cast<PlayObject*>(obj);

            // 检查是否是红名玩家
            if (player->GetPKPoint() >= 200) {
                // 设为攻击目标
                m_currentTarget = player;
                SetTargetCreature(player);

                // 警告
                Say(player->GetCharName() + "，你这个罪犯！受死吧！");

                // TODO: 发起攻击
                break;
            }
        }
    }
}

} // namespace MirServer