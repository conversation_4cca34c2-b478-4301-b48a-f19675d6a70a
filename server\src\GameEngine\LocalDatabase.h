#pragma once

#include "../Common/Types.h"
#include "../Database/IDatabase.h"
#include "../Common/Logger.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <mutex>
#include <shared_mutex>
#include <functional>

namespace MirServer {

// 前向声明
class PlayObject;
class NPC;
class Monster;
class TMerchant;
class Environment;
class UserEngine;

// 定义信息结构（对应原版TDefineInfo）
struct DefineInfo {
    std::string name;
    std::string text;
};

// QDD信息结构（对应原版TQDDinfo）
struct QDDInfo {
    int value = 0;
    std::string data;
    std::vector<std::string> list;
};

// 物品掉落信息
struct MonsterDropInfo {
    std::string itemName;
    int count = 1;
    double probability = 0.0; // 概率（0.0-1.0）
};

// 标准物品信息（对应原版pTStdItem）
struct StdItemInfo {
    int idx = 0;                // 物品索引
    std::string name;           // 物品名称
    int stdMode = 0;            // 物品类型
    int shape = 0;              // 外观
    int weight = 0;             // 重量
    int aniCount = 0;           // 动画帧数
    int source = 0;             // 来源
    int reserved = 0;           // 保留字段
    int looks = 0;              // 外观索引
    WORD duraMax = 0;           // 最大持久度
    TAbilityRange ac{0, 0};     // 防御力
    TAbilityRange mac{0, 0};    // 魔法防御力  
    TAbilityRange dc{0, 0};     // 攻击力
    TAbilityRange mc{0, 0};     // 魔法攻击力
    TAbilityRange sc{0, 0};     // 道术攻击力
    int need = 0;               // 需求属性
    int needLevel = 0;          // 需求等级
    int price = 0;              // 价格
    bool needIdentify = false;  // 是否需要鉴定
};

// 魔法信息（对应原版pTMagic）
struct MagicInfo {
    WORD magicId = 0;           // 魔法ID
    std::string name;           // 魔法名称
    BYTE effectType = 0;        // 效果类型
    BYTE effect = 0;            // 效果
    WORD spell = 0;             // 魔法值消耗
    WORD power = 0;             // 威力
    WORD maxPower = 0;          // 最大威力
    BYTE job = 0;               // 职业限制
    std::array<int, 4> trainLevel{0, 0, 0, 0}; // 各级别修炼等级要求
    std::array<int, 4> maxTrain{0, 0, 0, 0};   // 各级别最大修炼值
    BYTE trainLv = 3;           // 训练等级
    DWORD delayTime = 0;        // 延迟时间
    BYTE defSpell = 0;          // 默认魔法值消耗
    BYTE defPower = 0;          // 默认威力
    BYTE defMaxPower = 0;       // 默认最大威力
    std::string description;    // 描述
};

// 怪物信息
struct MonsterInfo {
    int race = 0;               // 种族
    std::string name;           // 怪物名称
    int level = 1;              // 等级
    TAbility ability;           // 基础属性
    std::vector<MonsterDropInfo> dropList; // 掉落列表
    int aiType = 0;             // AI类型
    int viewRange = 5;          // 视野范围
    int moveSpeed = 1000;       // 移动速度
    int attackSpeed = 1000;     // 攻击速度
    bool undead = false;        // 是否不死系
};

// 管理员信息（对应原版pTAdminInfo）
struct AdminInfo {
    int level = 0;              // 管理员级别
    std::string charName;       // 角色名
    std::string ipAddress;      // IP地址
};

// 守卫信息
struct GuardInfo {
    std::string name;           // 守卫名称
    std::string monsterName;    // 怪物名称
    std::string mapName;        // 地图名称
    Point position{0, 0};       // 位置
    DirectionType direction = DirectionType::DOWN; // 方向
};

// 制作物品信息
struct MakeItemInfo {
    std::string resultItem;     // 结果物品
    std::vector<std::pair<std::string, int>> materials; // 材料列表（物品名，数量）
};

// 地图事件信息
struct MapEventInfo {
    std::string eventType;      // 事件类型
    std::string mapName;        // 地图名称
    Point position{0, 0};       // 位置
    int range = 0;              // 范围
    std::string param;          // 参数
    int value = 0;              // 数值
};

// NPC脚本信息
struct NPCScriptInfo {
    std::string npcName;        // NPC名称
    std::string scriptPath;     // 脚本路径
    std::string scriptName;     // 脚本名称
    std::vector<std::string> scriptLines; // 脚本内容
};

// 商店物品信息
struct ShopItemInfo {
    std::string itemName;       // 物品名称
    int price = 0;              // 价格
    int count = 0;              // 数量（0表示无限）
    int level = 0;              // 物品等级
};

// 商人信息
struct MerchantInfo {
    std::string name;           // 商人名称
    std::string mapName;        // 地图名称
    Point position{0, 0};       // 位置
    std::vector<ShopItemInfo> goods; // 商品列表
    std::vector<ShopItemInfo> priceList; // 价格列表
};

// 升级武器记录
struct UpgradeWeaponRecord {
    std::string itemName;       // 武器名称
    int successRate = 0;        // 成功率
    std::vector<std::string> materials; // 材料需求
    int gold = 0;               // 金币需求
};

// 本地数据库缓存类（对应原版TFrmDB）
class LocalDatabase {
public:
    LocalDatabase();
    ~LocalDatabase();

    // 初始化和清理
    bool Initialize(std::shared_ptr<IDatabase> database, UserEngine* userEngine);
    void Finalize();

    // 数据加载方法（对应原版LoadXXX方法）
    bool LoadItemsDB();
    bool LoadMagicDB();
    bool LoadMonsterDB();
    bool LoadAdminList();
    bool LoadGuardList();
    bool LoadNPCs();
    bool LoadMerchant();
    bool LoadMakeItem();
    bool LoadMapInfo();
    bool LoadMapEvent();
    bool LoadStartPoint();
    bool LoadMinMap();
    bool LoadMapQuest();
    bool LoadQuestDiary();
    bool LoadUnbindList();
    bool LoadMonGen();

    // NPC脚本加载
    bool LoadNPCScript(const std::string& npcName, const std::string& scriptPath, const std::string& scriptName);
    bool LoadScriptFile(const std::string& npcName, const std::string& scriptPath, const std::string& scriptName, bool flag = false);

    // 商店数据加载和保存
    bool LoadGoodRecord(const std::string& npcName, const std::string& fileName);
    bool LoadGoodPriceRecord(const std::string& npcName, const std::string& fileName);
    bool SaveGoodRecord(const std::string& npcName, const std::string& fileName);
    bool SaveGoodPriceRecord(const std::string& npcName, const std::string& fileName);

    // 升级武器记录
    bool LoadUpgradeWeaponRecord(const std::string& npcName, std::vector<UpgradeWeaponRecord>& records);
    bool SaveUpgradeWeaponRecord(const std::string& npcName, const std::vector<UpgradeWeaponRecord>& records);

    // 重新加载数据
    void ReloadMerchants();
    void ReloadNPC();
    void ReloadAll();

    // 怪物掉落物品加载
    int LoadMonsterItems(const std::string& monsterName, std::vector<MonsterDropInfo>& itemList);

    // 数据查询接口
    const StdItemInfo* GetStdItem(int idx) const;
    const StdItemInfo* GetStdItemByName(const std::string& name) const;
    const MagicInfo* GetMagicInfo(WORD magicId) const;
    const MonsterInfo* GetMonsterInfo(const std::string& name) const;
    const AdminInfo* GetAdminInfo(const std::string& charName) const;
    const MerchantInfo* GetMerchantInfo(const std::string& name) const;

    // 缓存管理
    void ClearCache();
    void RefreshCache();
    size_t GetCacheSize() const;

    // 配置管理
    void SetEnvironmentDir(const std::string& dir) { m_environmentDir = dir; }
    const std::string& GetEnvironmentDir() const { return m_environmentDir; }

    // 统计信息
    struct Statistics {
        size_t stdItemCount = 0;
        size_t magicCount = 0;
        size_t monsterCount = 0;
        size_t adminCount = 0;
        size_t merchantCount = 0;
        size_t guardCount = 0;
        size_t npcCount = 0;
        DWORD lastLoadTime = 0;
        DWORD totalLoadTime = 0;
    };
    Statistics GetStatistics() const;

    // 事件回调
    using DataLoadedCallback = std::function<void(const std::string& dataType, bool success)>;
    void SetDataLoadedCallback(DataLoadedCallback callback) { m_dataLoadedCallback = callback; }

private:
    // 内部辅助方法
    bool LoadFromDatabase(const std::string& sql, std::function<void(const ResultSet&)> processor);
    bool LoadFromFile(const std::string& fileName, std::function<void(const std::vector<std::string>&)> processor);
    bool SaveToFile(const std::string& fileName, const std::vector<std::string>& lines);
    
    // 数据解析方法
    void ParseStdItemData(const ResultSet& resultSet);
    void ParseMagicData(const ResultSet& resultSet);
    void ParseMonsterData(const ResultSet& resultSet);
    void ParseAdminList(const std::vector<std::string>& lines);
    void ParseGuardList(const std::vector<std::string>& lines);
    void ParseMerchantData(const std::vector<std::string>& lines);
    void ParseMakeItemData(const std::vector<std::string>& lines);
    void ParseMapEventData(const std::vector<std::string>& lines);

    // 字符串处理辅助函数
    std::vector<std::string> SplitString(const std::string& str, const std::string& delimiters);
    std::string TrimString(const std::string& str);
    bool IsCommentLine(const std::string& line);
    
    // 文件操作辅助函数
    bool FileExists(const std::string& fileName) const;
    std::string GetFullPath(const std::string& fileName) const;
    std::vector<std::string> ReadFileLines(const std::string& fileName);
    bool WriteFileLines(const std::string& fileName, const std::vector<std::string>& lines);

private:
    // 数据库连接
    std::shared_ptr<IDatabase> m_database;
    UserEngine* m_userEngine;

    // 缓存数据
    std::vector<std::unique_ptr<StdItemInfo>> m_stdItems;
    std::unordered_map<std::string, size_t> m_stdItemNameIndex; // 名称到索引的映射
    
    std::vector<std::unique_ptr<MagicInfo>> m_magics;
    std::unordered_map<WORD, size_t> m_magicIndex; // ID到索引的映射
    
    std::vector<std::unique_ptr<MonsterInfo>> m_monsters;
    std::unordered_map<std::string, size_t> m_monsterIndex; // 名称到索引的映射
    
    std::vector<std::unique_ptr<AdminInfo>> m_admins;
    std::unordered_map<std::string, size_t> m_adminIndex; // 角色名到索引的映射
    
    std::vector<std::unique_ptr<MerchantInfo>> m_merchants;
    std::unordered_map<std::string, size_t> m_merchantIndex; // 名称到索引的映射
    
    std::vector<GuardInfo> m_guards;
    std::vector<MakeItemInfo> m_makeItems;
    std::vector<MapEventInfo> m_mapEvents;
    std::vector<NPCScriptInfo> m_npcScripts;

    // 线程安全
    mutable std::shared_mutex m_stdItemMutex;
    mutable std::shared_mutex m_magicMutex;
    mutable std::shared_mutex m_monsterMutex;
    mutable std::shared_mutex m_adminMutex;
    mutable std::shared_mutex m_merchantMutex;
    mutable std::mutex m_guardMutex;
    mutable std::mutex m_makeItemMutex;
    mutable std::mutex m_mapEventMutex;
    mutable std::mutex m_npcScriptMutex;

    // 配置
    std::string m_environmentDir = "./GameData/Envir/";
    bool m_initialized = false;

    // 统计信息
    Statistics m_statistics;
    mutable std::mutex m_statsMutex;

    // 事件回调
    DataLoadedCallback m_dataLoadedCallback;

    // 日志
    Logger* m_logger;
};

// 全局本地数据库实例
extern std::unique_ptr<LocalDatabase> g_LocalDatabase;

} // namespace MirServer 