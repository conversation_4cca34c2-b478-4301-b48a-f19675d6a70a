// MessageConverter.cpp - 消息转换器实现
#include "MessageConverter.h"
#include <algorithm>

namespace MirServer {
namespace Protocol {

// ==================== 基础编码函数 ====================

void MessageConverter::EncodeHeader(std::vector<uint8_t>& buffer, const PacketHeader& header) {
    EncodeValue(buffer, header.length);
    EncodeValue(buffer, header.packetType);
    EncodeValue(buffer, header.sequence);
}

void MessageConverter::EncodeByte(std::vector<uint8_t>& buffer, uint8_t value) {
    buffer.push_back(value);
}

void MessageConverter::EncodeWord(std::vector<uint8_t>& buffer, uint16_t value) {
    EncodeValue(buffer, value);
}

void MessageConverter::EncodeDWord(std::vector<uint8_t>& buffer, uint32_t value) {
    EncodeValue(buffer, value);
}

void MessageConverter::EncodeString(std::vector<uint8_t>& buffer, const std::string& str, size_t fixedSize) {
    if (fixedSize > 0) {
        // 固定长度字符串
        size_t copySize = std::min(str.length(), fixedSize);
        buffer.insert(buffer.end(), str.begin(), str.begin() + copySize);
        // 填充剩余空间
        if (copySize < fixedSize) {
            buffer.resize(buffer.size() + (fixedSize - copySize), 0);
        }
    } else {
        // 变长字符串，先写入长度
        EncodeWord(buffer, static_cast<uint16_t>(str.length()));
        buffer.insert(buffer.end(), str.begin(), str.end());
    }
}

void MessageConverter::EncodePoint(std::vector<uint8_t>& buffer, const Point& point) {
    EncodeWord(buffer, static_cast<uint16_t>(point.x));
    EncodeWord(buffer, static_cast<uint16_t>(point.y));
}

// ==================== 游戏对象编码 ====================

void MessageConverter::EncodeAbility(std::vector<uint8_t>& buffer, const Ability& ability) {
    EncodeWord(buffer, ability.level);
    EncodeWord(buffer, ability.ac);
    EncodeWord(buffer, ability.macAc);
    EncodeWord(buffer, ability.dc);
    EncodeWord(buffer, ability.mc);
    EncodeWord(buffer, ability.sc);
    EncodeWord(buffer, ability.hp);
    EncodeWord(buffer, ability.mp);
    EncodeWord(buffer, ability.maxHp);
    EncodeWord(buffer, ability.maxMp);
    EncodeDWord(buffer, ability.exp);
    EncodeDWord(buffer, ability.maxExp);
    EncodeWord(buffer, ability.weight);
    EncodeWord(buffer, ability.maxWeight);
    EncodeWord(buffer, ability.wearWeight);
    EncodeWord(buffer, ability.maxWearWeight);
    EncodeWord(buffer, ability.handWeight);
    EncodeWord(buffer, ability.maxHandWeight);
}

void MessageConverter::EncodeUserItem(std::vector<uint8_t>& buffer, const UserItem& item) {
    EncodeWord(buffer, item.makeIndex);
    EncodeWord(buffer, item.itemIndex);
    EncodeWord(buffer, item.dura);
    EncodeWord(buffer, item.duraMax);
    EncodeString(buffer, item.itemName, 32); // 固定32字节的物品名
    // 编码附加属性
    for (int i = 0; i < 14; i++) {
        EncodeByte(buffer, item.btValue[i]);
    }
}

void MessageConverter::EncodeHumDataInfo(std::vector<uint8_t>& buffer, const HumDataInfo& info) {
    EncodeString(buffer, info.charName, 32);
    EncodeString(buffer, info.account, 32);
    EncodeByte(buffer, static_cast<uint8_t>(info.gender));
    EncodeByte(buffer, static_cast<uint8_t>(info.job));
    EncodeByte(buffer, info.level);
    EncodeByte(buffer, static_cast<uint8_t>(info.direction));
    EncodePoint(buffer, info.currentPos);
    EncodeString(buffer, info.mapName, 32);
    EncodeDWord(buffer, info.gold);
    // 可以根据需要添加更多字段
}

// ==================== 登录相关消息编码 ====================

std::vector<uint8_t> MessageConverter::EncodeLoginSuccess(const std::string& message) {
    std::vector<uint8_t> buffer;
    
    // 预留包头空间
    buffer.resize(sizeof(PacketHeader));
    
    // 编码消息内容
    EncodeString(buffer, message);
    
    // 填充包头
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_CERTIFICATION_SUCCESS;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeLoginFail(int errorCode, const std::string& message) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, static_cast<uint32_t>(errorCode));
    EncodeString(buffer, message);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_CERTIFICATION_FAIL;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeCharacterList(const std::vector<HumDataInfo>& characters) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    // 编码角色数量
    EncodeByte(buffer, static_cast<uint8_t>(characters.size()));
    
    // 编码每个角色信息
    for (const auto& charInfo : characters) {
        EncodeString(buffer, charInfo.charName, 32);
        EncodeByte(buffer, static_cast<uint8_t>(charInfo.job));
        EncodeByte(buffer, static_cast<uint8_t>(charInfo.gender));
        EncodeByte(buffer, charInfo.level);
    }
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_QUERYCHR;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeStartPlay(const HumDataInfo& charInfo) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeHumDataInfo(buffer, charInfo);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_STARTPLAY;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

// ==================== 游戏内消息编码 ====================

std::vector<uint8_t> MessageConverter::EncodeMapChanged(const std::string& mapName, const Point& pos) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeString(buffer, mapName, 32);
    EncodePoint(buffer, pos);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_MAPCHANGED;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeAbilityUpdate(const Ability& ability) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeAbility(buffer, ability);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_ABILITY;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeBagItems(const std::vector<UserItem>& items) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeWord(buffer, static_cast<uint16_t>(items.size()));
    for (const auto& item : items) {
        EncodeUserItem(buffer, item);
    }
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_BAGITEMS;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeAddItem(const UserItem& item) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeUserItem(buffer, item);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_ADDITEM;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeDeleteItem(uint16_t makeIndex) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeWord(buffer, makeIndex);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_DELITEM;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeGoldChanged(uint32_t gold) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, gold);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_GOLDCHANGED;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeHealthChanged(uint16_t hp, uint16_t mp) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeWord(buffer, hp);
    EncodeWord(buffer, mp);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_HEALTHSPELLCHANGED;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

// ==================== 移动和动作消息编码 ====================

std::vector<uint8_t> MessageConverter::EncodeObjectWalk(uint32_t objectId, const Point& pos, DirectionType dir) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, objectId);
    EncodePoint(buffer, pos);
    EncodeByte(buffer, static_cast<uint8_t>(dir));
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_WALK;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeObjectRun(uint32_t objectId, const Point& pos, DirectionType dir) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, objectId);
    EncodePoint(buffer, pos);
    EncodeByte(buffer, static_cast<uint8_t>(dir));
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_RUN;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeObjectTurn(uint32_t objectId, DirectionType dir) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, objectId);
    EncodeByte(buffer, static_cast<uint8_t>(dir));
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_TURN;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeObjectAttack(uint32_t objectId, uint32_t targetId, DirectionType dir) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, objectId);
    EncodeDWord(buffer, targetId);
    EncodeByte(buffer, static_cast<uint8_t>(dir));
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_HIT;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

// ==================== NPC和商店消息编码 ====================

std::vector<uint8_t> MessageConverter::EncodeNPCDialog(uint32_t npcId, const std::string& text, const std::vector<std::string>& options) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeDWord(buffer, npcId);
    EncodeString(buffer, text);
    EncodeByte(buffer, static_cast<uint8_t>(options.size()));
    for (const auto& option : options) {
        EncodeString(buffer, option);
    }
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_MERCHANTDLG;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

std::vector<uint8_t> MessageConverter::EncodeShopItemList(const std::vector<std::pair<uint16_t, uint32_t>>& items) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeWord(buffer, static_cast<uint16_t>(items.size()));
    for (const auto& item : items) {
        EncodeWord(buffer, item.first);  // itemId
        EncodeDWord(buffer, item.second); // price
    }
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_SENDGOODSLIST;
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

// ==================== 基础解码函数 ====================

bool MessageConverter::DecodeHeader(const uint8_t* data, size_t dataSize, PacketHeader& header) {
    if (dataSize < sizeof(PacketHeader)) {
        return false;
    }
    std::memcpy(&header, data, sizeof(PacketHeader));
    return header.length >= sizeof(PacketHeader) && header.length <= 65535;
}

bool MessageConverter::DecodeByte(const uint8_t*& data, size_t& remaining, uint8_t& value) {
    return DecodeValue(data, remaining, value);
}

bool MessageConverter::DecodeWord(const uint8_t*& data, size_t& remaining, uint16_t& value) {
    return DecodeValue(data, remaining, value);
}

bool MessageConverter::DecodeDWord(const uint8_t*& data, size_t& remaining, uint32_t& value) {
    return DecodeValue(data, remaining, value);
}

bool MessageConverter::DecodeString(const uint8_t*& data, size_t& remaining, std::string& str, size_t fixedSize) {
    if (fixedSize > 0) {
        // 固定长度字符串
        if (remaining < fixedSize) {
            return false;
        }
        // 查找字符串结束位置
        size_t strLen = 0;
        while (strLen < fixedSize && data[strLen] != 0) {
            strLen++;
        }
        str.assign(reinterpret_cast<const char*>(data), strLen);
        data += fixedSize;
        remaining -= fixedSize;
    } else {
        // 变长字符串
        uint16_t strLen;
        if (!DecodeWord(data, remaining, strLen)) {
            return false;
        }
        if (remaining < strLen) {
            return false;
        }
        str.assign(reinterpret_cast<const char*>(data), strLen);
        data += strLen;
        remaining -= strLen;
    }
    return true;
}

bool MessageConverter::DecodePoint(const uint8_t*& data, size_t& remaining, Point& point) {
    uint16_t x, y;
    if (!DecodeWord(data, remaining, x) || !DecodeWord(data, remaining, y)) {
        return false;
    }
    point.x = static_cast<int>(x);
    point.y = static_cast<int>(y);
    return true;
}

// ==================== 登录相关消息解码 ====================

bool MessageConverter::DecodeLoginRequest(const uint8_t* data, size_t dataSize, LoginRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeString(ptr, remaining, request.account, 32)) return false;
    if (!DecodeString(ptr, remaining, request.password, 32)) return false;
    if (!DecodeByte(ptr, remaining, request.clientVersion)) return false;
    
    return true;
}

bool MessageConverter::DecodeCharacterCreate(const uint8_t* data, size_t dataSize, CharacterCreateRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeString(ptr, remaining, request.charName, 32)) return false;
    if (!DecodeByte(ptr, remaining, request.job)) return false;
    if (!DecodeByte(ptr, remaining, request.gender)) return false;
    if (!DecodeByte(ptr, remaining, request.hair)) return false;
    
    return true;
}

bool MessageConverter::DecodeCharacterSelect(const uint8_t* data, size_t dataSize, CharacterSelectRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeString(ptr, remaining, request.charName, 32)) return false;
    
    return true;
}

// ==================== 游戏内消息解码 ====================

bool MessageConverter::DecodeMoveRequest(const uint8_t* data, size_t dataSize, MoveRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodePoint(ptr, remaining, request.targetPos)) return false;
    
    uint8_t dir;
    if (!DecodeByte(ptr, remaining, dir)) return false;
    request.direction = static_cast<DirectionType>(dir);
    
    return true;
}

bool MessageConverter::DecodeAttackRequest(const uint8_t* data, size_t dataSize, AttackRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeDWord(ptr, remaining, request.targetId)) return false;
    
    uint8_t dir;
    if (!DecodeByte(ptr, remaining, dir)) return false;
    request.direction = static_cast<DirectionType>(dir);
    
    return true;
}

bool MessageConverter::DecodeChatMessage(const uint8_t* data, size_t dataSize, ChatMessage& message) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeByte(ptr, remaining, message.chatType)) return false;
    if (!DecodeString(ptr, remaining, message.message)) return false;
    
    if (message.chatType == 1) { // 私聊
        if (!DecodeString(ptr, remaining, message.targetName)) return false;
    }
    
    return true;
}

bool MessageConverter::DecodeItemAction(const uint8_t* data, size_t dataSize, ItemActionRequest& request) {
    const uint8_t* ptr = data;
    size_t remaining = dataSize;
    
    if (!DecodeWord(ptr, remaining, request.makeIndex)) return false;
    if (!DecodeByte(ptr, remaining, request.action)) return false;
    if (!DecodeWord(ptr, remaining, request.targetPos)) return false;
    
    return true;
}

// ==================== 工具函数 ====================

size_t MessageConverter::GetStringByteLength(const std::string& str) {
    // 在传奇协议中，中文字符通常占用2字节
    size_t byteLength = 0;
    for (size_t i = 0; i < str.length(); ) {
        uint8_t ch = static_cast<uint8_t>(str[i]);
        if (ch < 0x80) {
            // ASCII字符
            byteLength++;
            i++;
        } else {
            // 多字节字符（中文）
            byteLength += 2;
            // 跳过UTF-8编码的字节
            if ((ch & 0xE0) == 0xC0) i += 2;
            else if ((ch & 0xF0) == 0xE0) i += 3;
            else if ((ch & 0xF8) == 0xF0) i += 4;
            else i++;
        }
    }
    return byteLength;
}

bool MessageConverter::ValidatePacket(const uint8_t* data, size_t dataSize) {
    if (dataSize < sizeof(PacketHeader)) {
        return false;
    }
    
    const PacketHeader* header = reinterpret_cast<const PacketHeader*>(data);
    return header->length == dataSize && header->length <= 65535;
}

std::vector<uint8_t> MessageConverter::CreateErrorResponse(uint16_t errorCode, const std::string& errorMsg) {
    std::vector<uint8_t> buffer;
    
    buffer.resize(sizeof(PacketHeader));
    
    EncodeWord(buffer, errorCode);
    EncodeString(buffer, errorMsg);
    
    PacketHeader header;
    header.length = static_cast<uint16_t>(buffer.size());
    header.packetType = SM_OUTOFCONNECTION; // 通用错误响应
    header.sequence = 0;
    
    std::memcpy(buffer.data(), &header, sizeof(PacketHeader));
    
    return buffer;
}

// ==================== DefaultMessage编解码 ====================

// 6字节编码表（与delphi原版兼容）
static const char ENCODE_TABLE[64] = {
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
    'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
    'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd',
    'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
    'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x',
    'y', 'z', '_', '='
};

static uint8_t DecodeChar(char c) {
    if (c >= '0' && c <= '9') return c - '0';
    if (c >= 'A' && c <= 'Z') return c - 'A' + 10;
    if (c >= 'a' && c <= 'z') return c - 'a' + 36;
    if (c == '_') return 62;
    if (c == '=') return 63;
    return 0;
}

std::string MessageConverter::EncodeMessage(const DefaultMessage& msg) {
    // 将12字节的DefaultMessage编码为16字符的字符串
    uint8_t data[12];
    std::memcpy(data + 0, &msg.nRecog, 4);
    std::memcpy(data + 4, &msg.wIdent, 2);
    std::memcpy(data + 6, &msg.wParam, 2);
    std::memcpy(data + 8, &msg.wTag, 2);
    std::memcpy(data + 10, &msg.wSeries, 2);
    
    std::string result;
    result.reserve(16);
    
    // 每3字节编码为4字符
    for (int i = 0; i < 12; i += 3) {
        uint32_t value = 0;
        if (i < 12) value |= static_cast<uint32_t>(data[i]) << 16;
        if (i + 1 < 12) value |= static_cast<uint32_t>(data[i + 1]) << 8;
        if (i + 2 < 12) value |= static_cast<uint32_t>(data[i + 2]);
        
        result += ENCODE_TABLE[(value >> 18) & 0x3F];
        result += ENCODE_TABLE[(value >> 12) & 0x3F];
        result += ENCODE_TABLE[(value >> 6) & 0x3F];
        result += ENCODE_TABLE[value & 0x3F];
    }
    
    return result;
}

bool MessageConverter::DecodeMessage(const std::string& str, DefaultMessage& msg) {
    if (str.length() != 16) return false;
    
    uint8_t data[12] = {0};
    
    // 每4字符解码为3字节
    for (int i = 0; i < 16; i += 4) {
        uint32_t value = 0;
        value |= static_cast<uint32_t>(DecodeChar(str[i])) << 18;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 1])) << 12;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 2])) << 6;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 3]));
        
        int idx = (i / 4) * 3;
        if (idx < 12) data[idx] = (value >> 16) & 0xFF;
        if (idx + 1 < 12) data[idx + 1] = (value >> 8) & 0xFF;
        if (idx + 2 < 12) data[idx + 2] = value & 0xFF;
    }
    
    std::memcpy(&msg.nRecog, data + 0, 4);
    std::memcpy(&msg.wIdent, data + 4, 2);
    std::memcpy(&msg.wParam, data + 6, 2);
    std::memcpy(&msg.wTag, data + 8, 2);
    std::memcpy(&msg.wSeries, data + 10, 2);
    
    return true;
}

std::string MessageConverter::EncodeString(const std::string& str) {
    // 简单的字符串编码，将每个字符转换为6位编码
    std::string result;
    result.reserve((str.length() * 4 + 2) / 3);
    
    for (size_t i = 0; i < str.length(); i += 3) {
        uint32_t value = 0;
        value |= static_cast<uint32_t>(static_cast<uint8_t>(str[i])) << 16;
        if (i + 1 < str.length()) value |= static_cast<uint32_t>(static_cast<uint8_t>(str[i + 1])) << 8;
        if (i + 2 < str.length()) value |= static_cast<uint32_t>(static_cast<uint8_t>(str[i + 2]));
        
        result += ENCODE_TABLE[(value >> 18) & 0x3F];
        result += ENCODE_TABLE[(value >> 12) & 0x3F];
        result += ENCODE_TABLE[(value >> 6) & 0x3F];
        result += ENCODE_TABLE[value & 0x3F];
    }
    
    return result;
}

std::string MessageConverter::DecodeString(const std::string& str) {
    std::string result;
    
    for (size_t i = 0; i + 3 < str.length(); i += 4) {
        uint32_t value = 0;
        value |= static_cast<uint32_t>(DecodeChar(str[i])) << 18;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 1])) << 12;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 2])) << 6;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 3]));
        
        result += static_cast<char>((value >> 16) & 0xFF);
        if ((value >> 8) & 0xFF) result += static_cast<char>((value >> 8) & 0xFF);
        if (value & 0xFF) result += static_cast<char>(value & 0xFF);
    }
    
    // 移除末尾的空字符
    while (!result.empty() && result.back() == '\0') {
        result.pop_back();
    }
    
    return result;
}

std::string MessageConverter::EncodeBuffer(const void* data, size_t size) {
    const uint8_t* bytes = static_cast<const uint8_t*>(data);
    std::string result;
    result.reserve((size * 4 + 2) / 3);
    
    for (size_t i = 0; i < size; i += 3) {
        uint32_t value = 0;
        value |= static_cast<uint32_t>(bytes[i]) << 16;
        if (i + 1 < size) value |= static_cast<uint32_t>(bytes[i + 1]) << 8;
        if (i + 2 < size) value |= static_cast<uint32_t>(bytes[i + 2]);
        
        result += ENCODE_TABLE[(value >> 18) & 0x3F];
        result += ENCODE_TABLE[(value >> 12) & 0x3F];
        result += ENCODE_TABLE[(value >> 6) & 0x3F];
        result += ENCODE_TABLE[value & 0x3F];
    }
    
    return result;
}

bool MessageConverter::DecodeBuffer(const std::string& str, void* data, size_t size) {
    if (str.length() != ((size * 4 + 2) / 3)) return false;
    
    uint8_t* bytes = static_cast<uint8_t*>(data);
    size_t decoded = 0;
    
    for (size_t i = 0; i + 3 < str.length() && decoded < size; i += 4) {
        uint32_t value = 0;
        value |= static_cast<uint32_t>(DecodeChar(str[i])) << 18;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 1])) << 12;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 2])) << 6;
        value |= static_cast<uint32_t>(DecodeChar(str[i + 3]));
        
        if (decoded < size) bytes[decoded++] = (value >> 16) & 0xFF;
        if (decoded < size) bytes[decoded++] = (value >> 8) & 0xFF;
        if (decoded < size) bytes[decoded++] = value & 0xFF;
    }
    
    return decoded >= size;
}

} // namespace Protocol
} // namespace MirServer 